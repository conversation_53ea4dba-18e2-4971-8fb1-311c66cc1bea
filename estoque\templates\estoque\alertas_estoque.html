{% extends 'estoque/base.html' %}

{% block title %}Alertas de Estoque - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Alertas de Estoque</h1>

    <!-- Molas com estoque baixo -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="fas fa-exclamation-triangle"></i> Molas com Estoque Abaixo do Mínimo
            </h6>
        </div>
        <div class="card-body">
            {% if molas_estoque_baixo %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Cliente</th>
                                <th>Estoque Atual</th>
                                <th>Estoque Mínimo</th>
                                <th>Material</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mola in molas_estoque_baixo %}
                                <tr>
                                    <td>{{ mola.codigo }}</td>
                                    <td>{{ mola.cliente }}</td>
                                    <td class="text-danger fw-bold">{{ mola.quantidade_estoque }}</td>
                                    <td>{{ mola.estoque_minimo }}</td>
                                    <td>{{ mola.material.nome }} - {{ mola.material.bitola }}</td>
                                    <td>
                                        <a href="{% url 'mola-detail' mola.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Detalhes
                                        </a>
                                        <a href="{% url 'movimentacao-estoque-create' %}?mola={{ mola.id }}&tipo=E" class="btn btn-sm btn-success">
                                            <i class="fas fa-plus"></i> Entrada
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-success alert-dismissible fade show">
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    <i class="fas fa-check-circle"></i> Não há molas com estoque abaixo do mínimo.
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Materiais Padrão com estoque baixo -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="fas fa-exclamation-triangle"></i> Materiais Padrão com Estoque Abaixo do Mínimo
            </h6>
        </div>
        <div class="card-body">
            {% if materiais_padrao_estoque_baixo %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Diâmetro</th>
                                <th>Estoque Total (kg)</th>
                                <th>Estoque Mínimo (kg)</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material_padrao in materiais_padrao_estoque_baixo %}
                                <tr>
                                    <td>{{ material_padrao.nome }}</td>
                                    <td>{{ material_padrao.diametro }}</td>
                                    <td class="text-danger fw-bold">{{ material_padrao.estoque_total|floatformat:2 }}</td>
                                    <td>{{ material_padrao.estoque_minimo_total|floatformat:2 }}</td>
                                    <td>
                                        <a href="{% url 'material-padrao-detail' material_padrao.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> Detalhes
                                        </a>
                                        <a href="{% url 'material-padrao-update' material_padrao.id %}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Editar
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-success alert-dismissible fade show">
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    <i class="fas fa-check-circle"></i> Não há materiais padrão com estoque abaixo do mínimo.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
