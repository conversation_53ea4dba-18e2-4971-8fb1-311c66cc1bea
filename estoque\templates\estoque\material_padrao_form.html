{% extends 'estoque/base.html' %}

{% block title %}{% if form.instance.pk %}Editar{% else %}Novo{% endif %} Material Padrão - Molas <PERSON>s{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{% if form.instance.pk %}Editar{% else %}Novo{% endif %} Material Padrão</h1>
        <a href="{% url 'material-padrao-list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>
    
    <div class="card shadow">
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}
                
                <div class="col-md-6">
                    <label for="{{ form.nome.id_for_label }}" class="form-label">Nome *</label>
                    {{ form.nome.errors }}
                    <input type="text" name="{{ form.nome.name }}" id="{{ form.nome.id_for_label }}" class="form-control {% if form.nome.errors %}is-invalid{% endif %}" value="{{ form.nome.value|default:'' }}" required>
                    <div class="form-text">{{ form.nome.help_text }}</div>
                </div>
                
                <div class="col-md-6">
                    <label for="{{ form.diametro.id_for_label }}" class="form-label">Diâmetro *</label>
                    {{ form.diametro.errors }}
                    <input type="text" name="{{ form.diametro.name }}" id="{{ form.diametro.id_for_label }}" class="form-control {% if form.diametro.errors %}is-invalid{% endif %}" value="{{ form.diametro.value|default:'' }}" required>
                    <div class="form-text">{{ form.diametro.help_text }}</div>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.estoque_minimo.id_for_label }}" class="form-label">Estoque Mínimo (kg) *</label>
                    {{ form.estoque_minimo.errors }}
                    {{ form.estoque_minimo }}
                    <div class="form-text">{{ form.estoque_minimo.help_text }}</div>
                </div>
                
                <div class="col-12 mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                    <a href="{% url 'material-padrao-list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
