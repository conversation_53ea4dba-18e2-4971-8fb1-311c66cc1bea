import django_filters
from django import forms
from django.db.models import F
from .models import Mola, Material, MaterialPadrao, MovimentacaoEstoque, MovimentacaoMaterial, PedidoVenda


class MolaFilter(django_filters.FilterSet):
    codigo = django_filters.CharFilter(lookup_expr='icontains', label='Código')
    cliente = django_filters.CharFilter(lookup_expr='icontains', label='Cliente')
    material_padrao = django_filters.ModelChoiceFilter(
        queryset=MaterialPadrao.objects.all().order_by('nome'),
        label='Material Padrão',
        field_name='material_padrao'
    )
    estoque_baixo = django_filters.BooleanFilter(
        method='filter_estoque_baixo',
        label='Estoque abaixo do mínimo',
        widget=forms.CheckboxInput
    )

    class Meta:
        model = Mola
        fields = ['codigo', 'cliente', 'material_padrao', 'estoque_baixo']

    def filter_estoque_baixo(self, queryset, name, value):
        if value:
            return queryset.filter(quantidade_estoque__lte=F('estoque_minimo'))
        return queryset


class MaterialFilter(django_filters.FilterSet):
    nome = django_filters.CharFilter(lookup_expr='icontains', label='Nome')
    diametro = django_filters.CharFilter(lookup_expr='icontains', label='Diâmetro')
    fornecedor = django_filters.CharFilter(lookup_expr='icontains', label='Fornecedor')
    codigo_mola = django_filters.CharFilter(
        method='filter_codigo_mola',
        label='Código da Mola',
        help_text='Filtrar materiais compatíveis com a mola'
    )
    estoque_baixo = django_filters.BooleanFilter(
        method='filter_estoque_baixo',
        label='Estoque abaixo do mínimo',
        widget=forms.CheckboxInput
    )

    class Meta:
        model = Material
        fields = ['nome', 'diametro', 'fornecedor', 'codigo_mola', 'estoque_baixo']

    def filter_estoque_baixo(self, queryset, name, value):
        # Filtro removido - estoque mínimo agora é controlado no Material Padrão
        return queryset

    def filter_codigo_mola(self, queryset, name, value):
        """Filtrar materiais compatíveis com a mola especificada"""
        if value:
            from .models import Mola
            try:
                # Buscar a mola pelo código
                mola = Mola.objects.get(codigo=value, ativo=True)
                if mola.material_padrao:
                    # Filtrar apenas materiais que são variantes do material padrão da mola
                    return queryset.filter(material_padrao=mola.material_padrao)
                else:
                    # Se a mola não tem material padrão, retornar queryset vazio
                    return queryset.none()
            except Mola.DoesNotExist:
                # Se a mola não existe, retornar queryset vazio
                return queryset.none()
        return queryset


class MovimentacaoEstoqueFilter(django_filters.FilterSet):
    mola = django_filters.ModelChoiceFilter(
        queryset=Mola.objects.all(),
        label='Mola'
    )
    tipo = django_filters.ChoiceFilter(
        choices=MovimentacaoEstoque.TIPO_CHOICES,
        label='Tipo'
    )
    data_inicio = django_filters.DateFilter(
        field_name='data',
        lookup_expr='gte',
        label='Data inicial',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    data_fim = django_filters.DateFilter(
        field_name='data',
        lookup_expr='lte',
        label='Data final',
        widget=forms.DateInput(attrs={'type': 'date'})
    )

    class Meta:
        model = MovimentacaoEstoque
        fields = ['mola', 'tipo', 'data_inicio', 'data_fim']


class MovimentacaoMaterialFilter(django_filters.FilterSet):
    material = django_filters.ModelChoiceFilter(
        queryset=Material.objects.all(),
        label='Material'
    )
    tipo = django_filters.ChoiceFilter(
        choices=MovimentacaoMaterial.TIPO_CHOICES,
        label='Tipo'
    )
    data_inicio = django_filters.DateFilter(
        field_name='data',
        lookup_expr='gte',
        label='Data inicial',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    data_fim = django_filters.DateFilter(
        field_name='data',
        lookup_expr='lte',
        label='Data final',
        widget=forms.DateInput(attrs={'type': 'date'})
    )

    class Meta:
        model = MovimentacaoMaterial
        fields = ['material', 'tipo', 'data_inicio', 'data_fim']


class PedidoVendaFilter(django_filters.FilterSet):
    numero_pedido = django_filters.CharFilter(lookup_expr='icontains', label='Número do Pedido')
    cliente = django_filters.CharFilter(lookup_expr='icontains', label='Cliente')
    status = django_filters.ChoiceFilter(
        choices=PedidoVenda.STATUS_CHOICES,
        label='Status'
    )
    data_inicio = django_filters.DateFilter(
        field_name='data_pedido',
        lookup_expr='gte',
        label='Data inicial',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    data_fim = django_filters.DateFilter(
        field_name='data_pedido',
        lookup_expr='lte',
        label='Data final',
        widget=forms.DateInput(attrs={'type': 'date'})
    )

    class Meta:
        model = PedidoVenda
        fields = ['numero_pedido', 'cliente', 'status', 'data_inicio', 'data_fim']
