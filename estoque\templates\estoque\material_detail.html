{% extends 'estoque/base.html' %}

{% block title %}{{ material.nome }} - Detalhes do Material{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ material.nome }} - {{ material.bitola }}</h1>
        <div>
            <a href="{% url 'material-list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
            <a href="{% url 'material-update' material.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Editar
            </a>
            <a href="{% url 'movimentacao-material-create' %}?material={{ material.id }}&tipo=E" class="btn btn-success">
                <i class="fas fa-plus"></i> Entrada
            </a>
            <a href="{% url 'movimentacao-material-create' %}?material={{ material.id }}&tipo=S" class="btn btn-danger">
                <i class="fas fa-minus"></i> Saída
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Informações do Material -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informações do Material</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tr>
                                <th style="width: 30%">Nome:</th>
                                <td>{{ material.nome }}</td>
                            </tr>
                            <tr>
                                <th>Diâmetro:</th>
                                <td>{{ material.diametro }}</td>
                            </tr>
                            <tr>
                                <th>Descrição:</th>
                                <td>{{ material.descricao|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Quantidade em Estoque (kg):</th>
                                <td>
                                    <span class="text-success fw-bold">{{ material.quantidade_estoque }}</span>
                                    <span class="badge bg-info">Variante</span>
                                </td>
                            </tr>
                            {% if material.material_padrao %}
                            <tr>
                                <th>Material Padrão:</th>
                                <td>
                                    <a href="{% url 'material-padrao-detail' material.material_padrao.id %}">
                                        {{ material.material_padrao.nome }} - {{ material.material_padrao.diametro }}
                                    </a>
                                    <br>
                                    <small class="text-muted">
                                        Estoque Total: {{ material.material_padrao.calcular_estoque_total|floatformat:2 }} kg |
                                        Estoque Mínimo: {{ material.material_padrao.estoque_minimo|floatformat:2 }} kg
                                        {% if material.material_padrao.verificar_estoque_minimo %}
                                            <span class="badge bg-danger ms-1">Estoque Baixo</span>
                                        {% else %}
                                            <span class="badge bg-success ms-1">OK</span>
                                        {% endif %}
                                    </small>
                                </td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>Fornecedor:</th>
                                <td>{{ material.fornecedor|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Data:</th>
                                <td>{{ material.data|date:"d/m/Y"|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Nota Fiscal:</th>
                                <td>{{ material.nota_fiscal|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Data de Cadastro:</th>
                                <td>{{ material.data_cadastro|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <th>Última Atualização:</th>
                                <td>{{ material.data_atualizacao|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Molas que utilizam este material -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Molas que utilizam este material</h6>
                </div>
                <div class="card-body">
                    {% if molas %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Código</th>
                                        <th>Cliente</th>
                                        <th>Estoque</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mola in molas %}
                                        <tr>
                                            <td>{{ mola.codigo }}</td>
                                            <td>{{ mola.cliente }}</td>
                                            <td>
                                                {% if mola.quantidade_estoque <= mola.estoque_minimo %}
                                                    <span class="text-danger">{{ mola.quantidade_estoque }}</span>
                                                {% else %}
                                                    <span class="text-success">{{ mola.quantidade_estoque }}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'mola-detail' mola.id %}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Nenhuma mola utiliza este material.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Histórico de Movimentações -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">Histórico de Movimentações</h6>
                    <a href="{% url 'movimentacao-material-list' %}?material={{ material.id }}" class="btn btn-sm btn-primary">
                        Ver Todas
                    </a>
                </div>
                <div class="card-body">
                    {% if movimentacoes %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Data</th>
                                        <th>Tipo</th>
                                        <th>Quantidade</th>
                                        <th>Ordem de Compra</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mov in movimentacoes %}
                                        <tr>
                                            <td>{{ mov.data|date:"d/m/Y H:i" }}</td>
                                            <td>
                                                {% if mov.tipo == 'E' %}
                                                    <span class="badge bg-success">Entrada</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Saída</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ mov.quantidade }} kg</td>
                                            <td>{{ mov.ordem_compra|default:"--" }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Nenhuma movimentação registrada.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
